from typing import cast

from pocketflow import As<PERSON><PERSON><PERSON>, AsyncParallelBatchFlow, AsyncBatchFlow
from . import nodes


def create_crawl_shopify_product_page_flow():
    normalize_page_url = nodes.NormalizePageUrl()
    prepare_inject_js = nodes.LoadInjectJsCode()
    crawl_product_metadata = nodes.CrawlShopifyProductMetadata()
    crawl_product_web_page = nodes.CrawlShopifyProductWebPage()
    end = nodes.End()

    normalize_page_url >> prepare_inject_js
    prepare_inject_js >> crawl_product_metadata
    crawl_product_metadata >> crawl_product_web_page
    crawl_product_web_page >> end
    flow = AsyncFlow(start=normalize_page_url)
    return flow


def create_llm_format_html_flow():
    crawl_shopify_product_page_flow = create_crawl_shopify_product_page_flow()
    preprocess_html = nodes.PreprocessHTML()
    html_to_xml = nodes.HTMLToLLMFormat()

    crawl_shopify_product_page_flow - "end" >> preprocess_html >> html_to_xml

    flow = AsyncFlow(start=crawl_shopify_product_page_flow)
    return flow


def _create_ner_flow():
    """创建处理单个DOM节点的流程

    params:
        html: 原始页面
        product_id: 商品页 product id
        tag: 需要进行识别的 BeautifySoup 节点
        llm_conf: 大模型参数

    intermediate:
        element_tag: (optional) 命中的商品实体类型
        selling_point_summary: (optional) 商品卖点类型专用
    """
    ner = nodes.NER()
    to_main_image = nodes.ToMainImagePageElement()
    selling_point_indicator = nodes.SellingPointIndicator()
    selling_point_summary = nodes.SellingPointSummary()
    to_page_element = nodes.ToPageElement()
    need_recursive = nodes.NeedRecursive()
    end = nodes.End()

    # 从 NER 节点根据其输出字符串进行分支
    ner - "selling_point_indicator" >> selling_point_indicator
    ner - "main_image" >> to_main_image
    ner - "default" >> to_page_element
    ner - "continue" >> need_recursive

    # 商品卖点分支
    selling_point_indicator - "product_features" >> to_page_element
    selling_point_indicator - "selling_point" >> selling_point_summary
    selling_point_summary - "default" >> to_page_element
    selling_point_summary - "end" >> end

    to_page_element >> end

    # 单个节点的流程从 NER 开始
    flow = AsyncFlow(start=ner)
    return flow


class PageElementDetectionFlow(AsyncParallelBatchFlow):
    async def prep_async(self, shared):
        from bs4 import Tag
        from .utils import tag_need_ignore

        parent = cast(Tag, self.params["tag"])

        # 过滤掉不需要处理的子节点
        return [
            {"tag": child}
            for child in parent.children if not tag_need_ignore(child)
        ]


def create_page_element_detection_flow():
    """创建页面元素检测的主流程。"""
    # 1. 定义处理单个节点的子流程
    ner_flow = _create_ner_flow()
    end = nodes.End()
    # 2. 如果 NER 判断需要递归，则进入 BatchFlow
    children_flow = PageElementDetectionFlow(start=ner_flow)
    ner_flow - "continue" >> children_flow
    ner_flow - "end" >> end
    return children_flow



class SitePageElementDetectionFlow(AsyncBatchFlow):
    async def prep_async(self, shared):
        from crawl2.db import operations

        pages = await operations.list_shopify_domain_pages(self.params["domain"])
        return [
            {"page_url": page.page_url}
            for page in pages
        ]


def create_site_page_element_detection_flow(clear_existing: bool = False):
    pass

