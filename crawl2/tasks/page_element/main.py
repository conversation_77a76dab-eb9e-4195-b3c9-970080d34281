import time
from bs4 import Tag
from loguru import logger

from crawl2.clients.llm import LLMConfig


async def start_llm_format_html_flow(page_url: str):
    """将 html 转换为算法组需要的 xml 格式"""
    from .flow import create_llm_format_html_flow

    shared = {
        "site_domain": ...,
        "page_url": ...,
        "product_id": ...,
        "html": ...,
        "screenshot": ...,
        "html_llm_format": ...,
    }
    params = {
        "page_url": page_url,
    }
    flow = create_llm_format_html_flow()
    flow.set_params(params)
    await flow.run_async(shared)
    return shared


async def start_crawl_shopify_product_page_flow(
        page_url: str
):
    from .flow import create_crawl_shopify_product_page_flow

    shared = {
        "site_domain": ...,
        "page_url": ...,
        "product_id": ...,
        "html": ...,
        "screenshot": ...,
    }
    params = {
        "page_url": page_url,
    }
    flow = create_crawl_shopify_product_page_flow()
    flow.set_params(params)
    await flow.run_async(shared)
    return shared


async def start_page_element_detection_flow(
        html: str,
        product_id: str,
        detect_tag: Tag,
        llm_conf: LLMConfig
):
    """进行页面元素商品实体类型识别"""
    from .flow import create_page_element_detection_flow

    start_at = time.time()
    shared = {
        "page_elements": [],
        "debug": []
    }
    params = {
        "html": html,
        "product_id": product_id,
        "tag": detect_tag,
        "llm_conf": llm_conf
    }
    flow = create_page_element_detection_flow()
    flow.set_params(params)
    await flow.run_async(shared)
    duration = time.time() - start_at
    logger.info(f"page element detection flow finished in {duration:2f}s. "
                f"call llm {len(shared['debug'])} times. detect {len(shared['page_elements'])} element")
    return shared


async def start_crawl_top_50_flow():
    from bs4 import BeautifulSoup
    from .flow import create_crawl_shopify_product_page_flow, create_page_element_detection_flow

    from crawl2.db import operations
    from crawl2.utils import split_domain_and_path
    from crawl2.tasks.page_element.utils import LLM_CONF
    sites = [{'title': 'Hiut Denim Co.', 'url': 'https://hiutdenim.co.uk/'},
             {'title': 'Tentree', 'url': 'https://www.tentree.com/'},
             {'title': 'Maguire Shoes', 'url': 'https://maguireshoes.com/'},
             {'title': 'The Outrage', 'url': 'https://www.the-outrage.com/'},
             {'title': 'Adored Vintage', 'url': 'https://www.adoredvintage.com/'},
             {'title': 'Goodfair', 'url': 'https://goodfair.com/'},
             {'title': 'Kirrin Finch', 'url': 'https://kirrinfinch.com/'},
             {'title': "Rothy's", 'url': 'https://rothys.com/'},
             {'title': 'Beefcake Swimwear', 'url': 'https://www.beefcakeswimwear.com/'},
             {'title': 'Suta', 'url': 'https://suta.in/'},
             {'title': 'Uppercase magazine', 'url': 'https://uppercasemagazine.com/'},
             {'title': 'Artisaire', 'url': 'https://artisaire.com/'},
             {'title': 'Terre Bleu', 'url': 'https://www.terrebleu.ca/'},
             {'title': 'Silk and Willow', 'url': 'https://www.silkandwillow.com/'},
             {'title': 'GOODEE', 'url': 'https://www.goodeeworld.com/'},
             {'title': 'Bruvi', 'url': 'https://bruvi.com/'},
             {'title': 'Kulala', 'url': 'https://kulalaland.com/'},
             {'title': 'Pela', 'url': 'https://pelacase.ca/'},
             {'title': 'Cowboy', 'url': 'https://cowboy.com/'},
             {'title': 'Cocofloss', 'url': 'https://cocofloss.com/'},
             {'title': 'Loot Crate', 'url': 'https://lootcrate.com/'},
             {'title': 'Pot Gang', 'url': 'https://www.potgang.co.uk/'},
             {'title': 'United By Blue', 'url': 'https://unitedbyblue.com/'},
             {'title': 'Manitobah Mukluks', 'url': 'https://www.manitobah.com/'},
             {'title': 'Allbirds', 'url': 'https://www.allbirds.com/'},
             {'title': 'Camille Brinch', 'url': 'https://camillebrinch.com/'},
             {'title': 'Velasca', 'url': 'https://www.velasca.com/'},
             {'title': 'Troubadour Goods', 'url': 'https://www.troubadourgoods.com/'},
             {'title': 'BLK & Bold', 'url': 'https://blkandbold.com/'},
             {'title': 'Fly by Jing', 'url': 'https://flybyjing.com/'},
             {'title': 'Verve Coffee Roasters', 'url': 'https://www.vervecoffee.com/'},
             {'title': 'Taza Chocolate', 'url': 'https://www.tazachocolate.com/'},
             {'title': 'Flourist', 'url': 'https://flourist.com/'},
             {'title': 'The Honey Pot', 'url': 'https://thehoneypot.co/'},
             {'title': 'Package Free', 'url': 'https://packagefreeshop.com/'},
             {'title': 'Beauty Bakerie', 'url': 'https://www.beautybakerie.com/'},
             {'title': 'Cheekbone Beauty', 'url': 'https://www.cheekbonebeauty.com/'},
             {'title': 'Meow Meow Tweet', 'url': 'https://meowmeowtweet.com/'},
             {'title': 'Beneath Your Mask', 'url': 'https://beneathyourmask.com/'},
             {'title': 'Fresh Heritage', 'url': 'https://www.freshheritage.com/'},
             {'title': 'Then I Met You', 'url': 'https://thenimetyou.com/'},
             {'title': 'LastObject', 'url': 'https://lastobject.com/'},
             {'title': 'Tofino Soap Company', 'url': 'https://tofinosoapcompany.com/'},
             {'title': 'Satya', 'url': 'https://satyaorganics.com/'},
             {'title': 'GiveMeTap', 'url': 'https://www.givemetap.com/'},
             {'title': 'Lunchskins', 'url': 'https://www.lunchskins.com/'},
             {'title': 'Bebemoss', 'url': 'https://bebemoss.com/'},
             {'title': 'Made In', 'url': 'https://madeincookware.com/'}]
    params = []
    for site_info in sites:
        domain = split_domain_and_path(site_info["url"])[0]
        site = await operations.get_site(domain)
        if not site:
            continue
        if not site.product_urls:
            continue
        params.append({"page_url": site.product_urls[0]})
        page_url = "".join(split_domain_and_path(site.product_urls[0]))
        if not await operations.exists_page(page_url):
            await operations.create_page(domain, page_url)

    for idx, param in enumerate(params):
        logger.info(f"开始处理 {idx} {param}")
        shared = {
            "site_domain": ...,
            "page_url": ...,
            "product_id": ...,
            "html": ...,
            "screenshot": ...,

            "page_elements": [],
            "debug": []
        }
        crawl_flow = create_crawl_shopify_product_page_flow()
        crawl_flow.set_params(param)
        await crawl_flow.run_async(shared)
        if not BeautifulSoup(shared["html"], "html.parser").body:
            logger.info("no body tag, skip. ", shared["html"][:100])
            continue

        param.update(shared)
        param["tag"] = BeautifulSoup(shared["html"], "html.parser").body
        param["llm_conf"] = LLM_CONF
        detect_flow = create_page_element_detection_flow()
        detect_flow.set_params(param)
        start_at = time.time()
        try:
            await detect_flow.run_async(shared)
            duration = time.time() - start_at
            logger.info(
                f"page element detection flow finished in {duration:2f}s. "
                f"call llm {len(shared['debug'])} times. detect {len(shared['page_elements'])} element"
            )
            from crawl2.tasks.page_element.nodes import SaveShopifyPageElementToDB
            save = SaveShopifyPageElementToDB()
            await save.run_async(shared)
        except Exception as e:
            logger.error(f"failed with {shared['page_url']} {e}")
            pass
