import argparse
import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.resolve()))


async def main():
    from crawl2.tasks.page_element.main import start_llm_format_html_flow

    parser = argparse.ArgumentParser(description="Format HTML with llm_format_html_flow")
    parser.add_argument("--url", required=True, help="Page URL to fetch and process")
    parser.add_argument("-o", "--output", required=True, help="Output file to save formatted HTML")
    args = parser.parse_args()

    shared = await start_llm_format_html_flow(page_url=args.url)
    html = shared.get("html_llm_format", "")
    with open(args.output, "w", encoding="utf-8") as f:
        f.write(html)


if __name__ == "__main__":
    # example:
    # python offline_scripts/generate_page_html_llm_format.py --url https://www.ororowear.ca/products/women-s-junior-4-zone-heated-quilted-vest\?_pos\=1\&_fid\=d01c07722\&_ss\=c -o t.xml
    asyncio.run(main())
